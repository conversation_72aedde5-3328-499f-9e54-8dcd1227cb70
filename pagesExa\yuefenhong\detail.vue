<template>
  <view class="yuefenhong-detail-container">
    <!-- 顶部活动信息 -->
    <view class="activity-header">
      <view class="header-content">
        <view class="title-section">
          <text class="activity-title">{{activityDetail.title}}</text>
          <text class="activity-status" :class="activityDetail.status === 1 ? 'active' : 'ended'">
            {{getStatusText()}}
          </text>
        </view>
        <view class="activity-info">
          <view class="info-item">
            <text class="label">分红比例:</text>
            <text class="value">{{activityDetail.fenhong_ratio}}</text>
          </view>
          <view class="info-item">
            <text class="label">奖金池比例:</text>
            <text class="value">{{activityDetail.pool_ratio}}</text>
          </view>
          <view class="info-item">
            <text class="label">创建时间:</text>
            <text class="value">{{formatTime(activityDetail.createtime)}}</text>
          </view>
        </view>
      <!--  <view class="rules-section">
          <text class="section-title">分红规则</text>
          <view class="rules-content">
            <view class="rule-item">
              <text class="rule-label">参与等级:</text>
              <text class="rule-value">{{activityDetail.level_names || '无限制'}}</text>
            </view>
            <view class="rule-item">
              <text class="rule-label">业绩等级:</text>
              <text class="rule-value">{{activityDetail.yeji_level_names || '无限制'}}</text>
            </view>
            <view class="rule-item" v-if="activityDetail.deduct_contribution === 1">
              <text class="rule-label">贡献值扣除:</text>
              <text class="rule-value">{{activityDetail.contribution_ratio}}倍</text>
            </view>
          </view>
        </view> -->
      </view>
    </view>

    <!-- 数据概览 -->
    <view class="data-overview-section">
      <view class="overview-grid">
        <view class="overview-card">
          <text class="card-value">¥{{activityDetail.pool_amount || '0.00'}}</text>
          <text class="card-label">当前奖金池</text>
        </view>
        <view class="overview-card">
          <text class="card-value">¥{{activityDetail.total_amount || '0.00'}}</text>
          <text class="card-label">总分红金额</text>
        </view>
        <view class="overview-card">
          <text class="card-value">¥{{pendingAmount || '0.00'}}</text>
          <text class="card-label">待分红金额</text>
        </view>
        <view class="overview-card">
          <text class="card-value">{{participantCount || '0'}}</text>
          <text class="card-label">参与人数</text>
        </view>
      </view>
    </view>

    <!-- 分红明细列表 -->
   <!-- <view class="members-section">
      <view class="section-header">
        <text class="section-title">分红明细列表</text>
        <view class="header-actions">
          <button class="action-btn refresh-btn" @click="getDetailList">刷新</button>
        </view>
      </view> -->
      
    <!--  <view class="table-container">
        <view class="table-header">
          <view class="th">会员名称</view>
          <view class="th">分红金额</view>
          <view class="th">分红时间</view>
        </view>
        <view class="table-body">
          <view class="tr" v-for="(item, index) in detailList" :key="index">
            <view class="td">{{item.member_name}}</view>
            <view class="td amount">¥{{item.dividend_amount}}</view>
            <view class="td">{{formatTime(item.dividend_time)}}</view>
          </view>
        </view>
      </view> -->
      
      <!-- 分页控制 -->
      <!-- <view class="pagination">
        <view class="page-info">
          <text>共 {{totalItems}} 条记录，当前第 {{currentPage}}/{{totalPages}} 页</text>
        </view>
        <view class="page-controls">
          <button class="page-btn" :disabled="currentPage <= 1" @click="changePage(currentPage - 1)">上一页</button>
          <button class="page-btn" :disabled="currentPage >= totalPages" @click="changePage(currentPage + 1)">下一页</button>
        </view>
      </view> -->
      
      <!-- 加载状态组件 -->
      <nodata v-if="nodata"></nodata>
      <nomore v-if="nomore"></nomore>
      <loading v-if="loading"></loading>
    </view>
    
    <!-- 分红操作按钮 -->
   <!-- <view class="action-section" v-if="isAdmin">
      <button class="primary-btn" @click="triggerDividend">手动触发分红</button>
    </view> -->
    
    <!-- 分红确认弹窗 -->
    <!-- <view class="dividend-modal" v-if="showDividendModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">确认分红</text>
        </view>
        <view class="modal-body">
          <text class="modal-text">您确定要触发分红操作吗？此操作将按照当前奖金池金额和规则进行分红。</text>
          <view class="modal-info">
            <text class="info-label">当前奖金池:</text>
            <text class="info-value">¥{{activityDetail.pool_amount || '0.00'}}</text>
          </view>
          <view class="modal-info">
            <text class="info-label">预计分红人数:</text>
            <text class="info-value">{{participantCount || '0'}}人</text>
          </view>
        </view>
        <view class="modal-footer">
          <button class="cancel-btn" @click="showDividendModal = false">取消</button>
          <button class="confirm-btn" @click="confirmDividend">确认分红</button>
        </view>
      </view>
    </view> -->
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      activityId: '',
      activityDetail: {},
      detailList: [],
      pendingAmount: '0.00',
      participantCount: 0,
      currentPage: 1,
      pageSize: 10,
      totalItems: 0,
      totalPages: 1,
      loading: false,
      nodata: false,
      nomore: false,
      isAdmin: false, // 是否管理员
      showDividendModal: false
    }
  },
  onLoad(options) {
    this.activityId = options.id || ''
    this.getActivityDetail()
    this.getDetailList()
    this.checkAdminStatus()
  },
  onPullDownRefresh() {
    this.getActivityDetail()
    this.getDetailList()
    uni.stopPullDownRefresh()
  },
  methods: {
    getActivityDetail() {
      const that = this
      app.showLoading('加载中')
      app.post('ApiYuefenhong/getDetail', {
        aid: app.aid,
        id: this.activityId
      }, function(res) {
        app.showLoading(false)
        if (res.code === 1) {
          that.activityDetail = res.data
          
          // 计算待分红金额
          const totalAmount = parseFloat(that.activityDetail.total_amount || 0)
          const poolAmount = parseFloat(that.activityDetail.pool_amount || 0)
          that.pendingAmount = (poolAmount).toFixed(2)
          
          // 获取参与人数（这里可能需要额外的API调用）
          that.getParticipantCount()
        } else {
          app.error(res.msg)
        }
      })
    },
    getDetailList() {
      const that = this
      that.loading = true
      app.showLoading('加载中')
      app.post('ApiYuefenhong/getDetailList', {
        aid: app.aid,
        fenhong_id: this.activityId,
        page: this.currentPage,
        limit: this.pageSize
      }, function(res) {
        that.loading = false
        app.showLoading(false)
        if (res.code === 1) {
          const { list, count } = res.data
          that.detailList = list
          that.totalItems = count
          that.totalPages = Math.ceil(count / that.pageSize)
          that.nodata = list.length === 0
          that.nomore = that.currentPage >= that.totalPages
        } else {
          app.error(res.msg)
        }
      })
    },
    getParticipantCount() {
      // 这里可能需要额外的API调用来获取参与人数
      // 示例代码，实际实现可能不同
      const that = this
      app.post('ApiYuefenhong/getParticipantCount', {
        aid: app.aid,
        id: this.activityId
      }, function(res) {
        if (res.code === 1) {
          that.participantCount = res.data.count || 0
        }
      })
    },
    checkAdminStatus() {
      // 检查当前用户是否有管理员权限
      // 示例代码，实际实现可能不同
      const that = this
      app.post('ApiYuefenhong/checkAdminPermission', {
        aid: app.aid
      }, function(res) {
        if (res.code === 1) {
          that.isAdmin = res.data.is_admin || false
        }
      })
    },
    formatTime(time) {
      if (!time) return ''
      return time.substring(0, 16) // 只显示到分钟
    },
    getStatusClass() {
      // 这里根据实际状态判断逻辑修改
      return this.activityDetail.status === 1 ? 'active' : 'ended'
    },
    getStatusText() {
      // 这里根据实际状态判断逻辑修改
      return this.activityDetail.status === 1 ? '进行中' : '已结束'
    },
    changePage(page) {
      if (page < 1 || page > this.totalPages) return
      this.currentPage = page
      this.getDetailList()
    },
    triggerDividend() {
      this.showDividendModal = true
    },
    confirmDividend() {
      const that = this
      app.showLoading('处理中')
      app.post('ApiYuefenhong/triggerDividend', {
        aid: app.aid,
        id: this.activityId
      }, function(res) {
        app.showLoading(false)
        that.showDividendModal = false
        if (res.code === 1) {
          app.success(res.msg || '分红操作成功')
          // 刷新数据
          that.getActivityDetail()
          that.getDetailList()
        } else {
          app.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.yuefenhong-detail-container {
  min-height: 100vh;
  background: #f8f9fd;
  padding-bottom: 120rpx;
  
  ::v-deep .nodata, ::v-deep .nomore, ::v-deep .loading {
    position: relative;
    margin: 30rpx auto;
    text-align: center;
  }
}

.activity-header {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  padding: 40rpx 30rpx;
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 241, 0.3);
  
  .header-content {
    .title-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      
      .activity-title {
        font-size: 40rpx;
        font-weight: bold;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }
      
      .activity-status {
        font-size: 24rpx;
        padding: 8rpx 20rpx;
        border-radius: 30rpx;
        background: rgba(255, 255, 255, 0.2);
        
        &.active {
          background: #10b981;
        }
        
        &.ended {
          background: #6b7280;
        }
      }
    }
    
    .activity-info {
      margin-bottom: 30rpx;
      
      .info-item {
        display: flex;
        margin-bottom: 10rpx;
        
        .label {
          font-size: 28rpx;
          opacity: 0.9;
          margin-right: 20rpx;
        }
        
        .value {
          font-size: 28rpx;
          font-weight: bold;
        }
      }
    }
    
    .rules-section {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 16rpx;
      padding: 24rpx;
      
      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        display: block;
      }
      
      .rules-content {
        .rule-item {
          display: flex;
          margin-bottom: 10rpx;
          
          .rule-label {
            font-size: 28rpx;
            opacity: 0.9;
            margin-right: 20rpx;
            min-width: 150rpx;
          }
          
          .rule-value {
            font-size: 28rpx;
            font-weight: bold;
          }
        }
      }
    }
  }
}

.data-overview-section {
  padding: 30rpx;
  
  .overview-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    margin-bottom: 30rpx;
    
    .overview-card {
      background: #fff;
      border-radius: 16rpx;
      padding: 30rpx;
      text-align: center;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      
      .card-value {
        font-size: 40rpx;
        font-weight: bold;
        color: #6366f1;
        display: block;
        margin-bottom: 10rpx;
      }
      
      .card-label {
        font-size: 28rpx;
        color: #6b7280;
      }
    }
  }
}

.members-section {
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 30rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #1f2937;
    }
    
    .header-actions {
      .action-btn {
        font-size: 24rpx;
        padding: 10rpx 20rpx;
        border-radius: 40rpx;
        background: #f3f4f6;
        color: #6b7280;
      }
    }
  }
  
  .table-container {
    width: 100%;
    overflow-x: auto;
    
    .table-header {
      display: flex;
      background: #f3f4f6;
      border-radius: 8rpx 8rpx 0 0;
      
      .th {
        flex: 1;
        padding: 20rpx;
        font-size: 28rpx;
        font-weight: bold;
        color: #1f2937;
        text-align: center;
      }
    }
    
    .table-body {
      .tr {
        display: flex;
        border-bottom: 2rpx solid #f3f4f6;
        
        &:last-child {
          border-bottom: none;
        }
        
        .td {
          flex: 1;
          padding: 20rpx;
          font-size: 28rpx;
          color: #6b7280;
          text-align: center;
          
          &.amount {
            color: #6366f1;
            font-weight: bold;
          }
        }
      }
    }
  }
  
  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30rpx;
    flex-wrap: wrap;
    
    .page-info {
      font-size: 24rpx;
      color: #6b7280;
    }
    
    .page-controls {
      display: flex;
      gap: 10rpx;
      
      .page-btn {
        font-size: 24rpx;
        padding: 10rpx 20rpx;
        border-radius: 40rpx;
        background: #f3f4f6;
        color: #6b7280;
        
        &:disabled {
          opacity: 0.5;
        }
      }
    }
  }
}

.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
  
  .primary-btn {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.3);
  }
}

.dividend-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .modal-content {
    width: 80%;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.15);
    
    .modal-header {
      padding: 30rpx;
      border-bottom: 2rpx solid #f3f4f6;
      
      .modal-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #1f2937;
      }
    }
    
    .modal-body {
      padding: 30rpx;
      
      .modal-text {
        font-size: 28rpx;
        color: #6b7280;
        margin-bottom: 30rpx;
        display: block;
      }
      
      .modal-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        
        .info-label {
          font-size: 28rpx;
          color: #6b7280;
        }
        
        .info-value {
          font-size: 28rpx;
          font-weight: bold;
          color: #1f2937;
        }
      }
    }
    
    .modal-footer {
      display: flex;
      border-top: 2rpx solid #f3f4f6;
      
      button {
        flex: 1;
        height: 100rpx;
        font-size: 32rpx;
        border: none;
        background: transparent;
        
        &.cancel-btn {
          color: #6b7280;
          border-right: 2rpx solid #f3f4f6;
        }
        
        &.confirm-btn {
          color: #6366f1;
          font-weight: bold;
        }
      }
    }
  }
}
</style> 