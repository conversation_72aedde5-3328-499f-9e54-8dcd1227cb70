<template>
  <view class="yuefenhong-container">
    <!-- 页面标题 -->
   <!-- <view class="page-title">
      <text>月分红活动</text>
    </view> -->
    
    <!-- 活动列表区 -->
    <view class="activities-list">
      <view class="activities-container">
        <view 
          v-for="(activity, index) in activities" 
          :key="index" 
          class="activity-card"
         
        >
          <view class="card-header">
            <text class="activity-title">{{activity.title}}</text>
            <!-- 状态已移除 -->
          </view>
          <view class="card-body">
            <view class="ratio-display">
              <text class="ratio-value">{{activity.fenhong_ratio}}</text>
              <text class="ratio-label">分红比例</text>
            </view>
            <view class="pool-info">
              <text class="pool-amount">¥{{activity.pool_amount}}</text>
              <text class="pool-label">奖金池金额</text>
            </view>
           <!-- <view class="participants-info">
              <text class="participants-count">{{activity.member_count || 0}}</text>
              <text class="participants-label">参与人数</text>
            </view> -->
          </view>
          <view class="card-footer">
            <text class="create-time">创建时间: {{formatTime(activity.createtime)}}</text>
           <!-- <view class="action-buttons">
              <button class="detail-btn" @click.stop="goToDetail(activity.id)">查看详情</button>
            </view> -->
          </view>
        </view>
      </view>
      
      <!-- 加载状态组件 -->
      <nodata v-if="nodata"></nodata>
      <nomore v-if="nomore"></nomore>
      <loading v-if="loading"></loading>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      activities: [],
      page: 1,
      limit: 10,
      loading: false,
      nodata: false,
      nomore: false
    }
  },
  onLoad() {
    this.getActivities()
  },
  onPullDownRefresh() {
    this.page = 1
    this.getActivities()
  },
  onReachBottom() {
    if (!this.nomore && !this.nodata) {
      this.page = this.page + 1
      this.getActivities(true)
    }
  },
  methods: {
    getActivities(loadmore) {
      if (!loadmore) {
        this.page = 1
        this.activities = []
      }
      
      this.loading = true
      const that = this
      
      app.showLoading('加载中')
      app.post('ApiYuefenhong/getList', {
        aid: app.aid,
        page: this.page,
        limit: this.limit
      }, function(res) {
        that.loading = false
        app.showLoading(false)
        
        if (res.code === 1) {
          const { list, count } = res.data
          
          if (that.page === 1) {
            that.activities = list
            that.nodata = list.length === 0
          } else {
            that.activities = [...that.activities, ...list]
          }
          
          that.nomore = that.activities.length >= count
          uni.stopPullDownRefresh()
        } else {
          app.error(res.msg)
        }
      })
    },
    formatTime(time) {
      if (!time) return ''
      return time.substring(0, 16) // 只显示到分钟
    },
    getStatusClass(activity) {
      // 这里根据实际状态判断逻辑修改
      return activity.status === 1 ? 'active' : 'ended'
    },
    getStatusText(activity) {
      // 这里根据实际状态判断逻辑修改
      return activity.status === 1 ? '进行中' : '已结束'
    },
    goToDetail(id) {
      app.goto(`/pagesExa/yuefenhong/detail?id=${id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.yuefenhong-container {
  min-height: 100vh;
  background: #f8f9fd;
  padding: 20rpx;
  
  ::v-deep .nodata, ::v-deep .nomore, ::v-deep .loading {
    position: relative;
    margin: 30rpx auto;
    text-align: center;
  }
}

.page-title {
  text-align: center;
  margin-bottom: 30rpx;
  
  text {
    font-size: 36rpx;
    font-weight: bold;
    color: #1f2937;
    position: relative;
    display: inline-block;
    padding-bottom: 16rpx;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 6rpx;
      background: linear-gradient(90deg, #6366f1, #8b5cf6);
      border-radius: 3rpx;
    }
  }
}

.activities-list {
  .activities-container {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
  
  .activity-card {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:active {
      transform: translateY(-5rpx);
      box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.12);
    }
    
    .card-header {
      padding: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2rpx solid #f3f4f6;
      
      .activity-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #1f2937;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .card-body {
      padding: 30rpx 24rpx;
      display: flex;
      justify-content: space-around;
      
      .ratio-display {
        text-align: center;
        
        .ratio-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #6366f1;
          display: block;
          margin-bottom: 10rpx;
        }
        
        .ratio-label {
          font-size: 24rpx;
          color: #6b7280;
        }
      }
      
      .pool-info, .participants-info {
        text-align: center;
        
        .pool-amount, .participants-count {
          font-size: 36rpx;
          font-weight: bold;
          color: #1f2937;
          display: block;
          margin-bottom: 10rpx;
        }
        
        .pool-label, .participants-label {
          font-size: 24rpx;
          color: #6b7280;
        }
      }
    }
    
    .card-footer {
      padding: 24rpx;
      border-top: 2rpx solid #f3f4f6;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      
      .create-time {
        font-size: 24rpx;
        color: #6b7280;
      }
      
      .action-buttons {
        .detail-btn {
          font-size: 26rpx;
          padding: 12rpx 30rpx;
          border-radius: 40rpx;
          background: #6366f1;
          color: #fff;
          box-shadow: 0 4rpx 8rpx rgba(99, 102, 241, 0.2);
        }
      }
    }
  }
}
</style> 